import { useState, useCallback } from 'react';
import type { ModalState } from '../types/user';

/**
 * Modal状态管理Hook
 * 提供通用的Modal显示/隐藏和加载状态管理
 */
export const useModalState = (initialVisible = false, initialLoading = false) => {
  const [visible, setVisible] = useState(initialVisible);
  const [loading, setLoading] = useState(initialLoading);

  /**
   * 显示Modal
   */
  const show = useCallback(() => {
    setVisible(true);
  }, []);

  /**
   * 隐藏Modal
   */
  const hide = useCallback(() => {
    setVisible(false);
    setLoading(false); // 隐藏时重置加载状态
  }, []);

  /**
   * 切换Modal显示状态
   */
  const toggle = useCallback(() => {
    setVisible(prev => !prev);
  }, []);

  /**
   * 设置加载状态
   */
  const setLoadingState = useCallback((loadingState: boolean) => {
    setLoading(loadingState);
  }, []);

  /**
   * 开始加载
   */
  const startLoading = useCallback(() => {
    setLoading(true);
  }, []);

  /**
   * 结束加载
   */
  const stopLoading = useCallback(() => {
    setLoading(false);
  }, []);

  /**
   * 获取Modal状态
   */
  const modalState: ModalState = {
    visible,
    loading,
  };

  return {
    visible,
    loading,
    show,
    hide,
    toggle,
    setLoadingState,
    startLoading,
    stopLoading,
    modalState,
  };
};

/**
 * 多个Modal状态管理Hook
 * 用于管理多个Modal的状态
 */
export const useMultiModalState = <T extends string>(modalKeys: T[]) => {
  const [modals, setModals] = useState<Record<T, ModalState>>(() => {
    const initialState = {} as Record<T, ModalState>;
    modalKeys.forEach(key => {
      initialState[key] = {
        visible: false,
        loading: false,
      };
    });
    return initialState;
  });

  /**
   * 显示指定Modal
   */
  const showModal = useCallback((key: T) => {
    setModals(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        visible: true,
      },
    }));
  }, []);

  /**
   * 隐藏指定Modal
   */
  const hideModal = useCallback((key: T) => {
    setModals(prev => ({
      ...prev,
      [key]: {
        visible: false,
        loading: false,
      },
    }));
  }, []);

  /**
   * 设置指定Modal的加载状态
   */
  const setModalLoading = useCallback((key: T, loading: boolean) => {
    setModals(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        loading,
      },
    }));
  }, []);

  /**
   * 切换指定Modal的显示状态
   */
  const toggleModal = useCallback((key: T) => {
    setModals(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        visible: !prev[key].visible,
      },
    }));
  }, []);

  return {
    modals,
    showModal,
    hideModal,
    setModalLoading,
    toggleModal,
  };
};
