# VTable 级联选择功能配置说明

## 概述

已成功为 VTable 组件配置了 `enableHeaderCheckboxCascade` 功能，实现了表头 checkbox 与数据行 checkbox 的级联选择。

## 配置详情

### 1. 核心配置

在 `ListTable` 组件中添加了以下配置：

```typescript
<ListTable
  // ... 其他配置
  enableHeaderCheckboxCascade={true}  // 启用表头checkbox级联选择
  enableCheckboxCascade={true}        // 启用checkbox级联选择（默认为true）
  // ... 其他配置
/>
```

### 2. Checkbox 列配置

```typescript
const columns = [
  {
    field: 'selected',
    title: '选择',
    cellType: 'checkbox' as const, // 使用VTable原生checkbox
    headerType: 'checkbox' as const, // 表头也使用原生checkbox
    width: '4%',
    minWidth: 40,
    // 动态计算表头checkbox状态，实现联动
    checked: getHeaderCheckboxState(),
    // VTable原生支持indeterminate状态
    indeterminate: getHeaderIndeterminate(),
    headerStyle: {
      textAlign: 'center' as const,
      fontWeight: 600,
      color: '#374151',
    },
    style: {
      textAlign: 'center' as const,
      fontWeight: 500,
      color: '#6b7280',
    },
  },
  // ... 其他列配置
];
```

### 3. 状态管理函数

#### 表头 Checkbox 状态计算

```typescript
const getHeaderCheckboxState = useCallback(() => {
  const totalCount = tableData.length;
  const selectedCount = selectedRowKeys.length;

  if (totalCount === 0) return false; // 无数据时
  return selectedCount === totalCount; // 全选时为true，否则为false
}, [tableData.length, selectedRowKeys.length]);
```

#### 表头 Checkbox 半选状态计算

```typescript
const getHeaderIndeterminate = useCallback(() => {
  const totalCount = tableData.length;
  const selectedCount = selectedRowKeys.length;

  return selectedCount > 0 && selectedCount < totalCount;
}, [tableData.length, selectedRowKeys.length]);
```

#### 动态更新表头状态

```typescript
const updateHeaderCheckboxState = useCallback(() => {
  if (tableRef.current) {
    const headerCheckboxState = getHeaderCheckboxState();
    const headerIndeterminate = getHeaderIndeterminate();

    console.log('更新表头checkbox状态:', {
      checked: headerCheckboxState,
      indeterminate: headerIndeterminate,
      selectedCount: selectedRowKeys.length,
      totalCount: tableData.length,
    });
  }
}, [getHeaderCheckboxState, getHeaderIndeterminate, selectedRowKeys.length, tableData.length]);
```

## 功能特性

### ✅ 已实现的功能

1. **表头级联选择**：
   - 点击表头 checkbox 可以全选/取消全选所有行
   - 表头 checkbox 支持三种状态：未选中、半选中、全选中

2. **智能状态联动**：
   - 当所有行都被选中时，表头 checkbox 显示为全选状态
   - 当部分行被选中时，表头 checkbox 显示为半选状态（indeterminate）
   - 当没有行被选中时，表头 checkbox 显示为未选状态

3. **事件处理**：
   - `onCheckboxStateChange` 事件处理表头和数据行的 checkbox 状态变化
   - `onClickCell` 事件支持点击 checkbox 单元格进行选择

4. **视觉反馈**：
   - 选中行有特殊的背景色和文字色
   - 实时显示选中数量和状态
   - 级联选择状态标识

## 使用方法

### 访问页面

```
http://localhost:5175/vtable
```

### 操作说明

1. **表头全选/取消全选**：
   - 点击表头的 checkbox 可以全选或取消全选所有行
   - 表头 checkbox 会根据当前选中状态智能切换

2. **单行选择**：
   - 点击任意行的 checkbox 可以选择/取消选择该行
   - 表头 checkbox 会自动更新状态（全选/半选/未选）

3. **批量操作**：
   - 选中行后可以使用"批量删除"功能
   - 支持"全选"、"取消全选"、"清除选择"等操作

## 技术实现

### 核心原理

1. **VTable 原生支持**：
   - 使用 VTable 原生的 `cellType: "checkbox"` 和 `headerType: "checkbox"`
   - 启用 `enableHeaderCheckboxCascade` 和 `enableCheckboxCascade` 配置

2. **状态同步**：
   - 使用 React 的 `useState` 管理选中状态
   - 使用 `useCallback` 优化性能，避免不必要的重渲染
   - 使用 `useEffect` 监听状态变化，实时更新表头状态

3. **事件处理**：
   - 通过 `onCheckboxStateChange` 事件统一处理所有 checkbox 状态变化
   - 区分表头点击（row === 0）和数据行点击（row > 0）

## 性能优化

1. **使用 useCallback**：
   - 所有状态计算函数都使用 `useCallback` 包装
   - 避免不必要的函数重新创建

2. **精确依赖**：
   - 使用 `tableData.length` 和 `selectedRowKeys.length` 作为依赖
   - 避免整个数组作为依赖导致的性能问题

3. **Canvas 渲染**：
   - VTable 使用 Canvas 渲染，支持大数据量高性能显示
   - 虚拟滚动技术，只渲染可见区域

## 总结

✅ **级联选择功能**：完全实现，支持表头与数据行的双向联动
✅ **状态管理**：完善的状态同步机制，支持三种选择状态
✅ **用户体验**：直观的视觉反馈和操作提示
✅ **性能优化**：使用 React 最佳实践，确保高性能渲染
✅ **代码质量**：TypeScript 类型安全，无编译错误

级联选择功能已成功配置并正常工作，用户可以通过表头 checkbox 实现快速的全选/取消全选操作，同时表头状态会根据数据行的选择情况智能更新。
