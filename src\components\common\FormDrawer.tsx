import React from 'react';
import { Drawer, Button } from 'antd';

/**
 * 表单抽屉组件Props
 */
interface FormDrawerProps {
  title: string;
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onReset?: () => void;
  children: React.ReactNode;
  width?: number;
  loading?: boolean;
  submitText?: string;
  resetText?: string;
  cancelText?: string;
  showReset?: boolean;
}

/**
 * 通用表单抽屉组件
 * 提供统一的抽屉样式和操作按钮
 */
export const FormDrawer: React.FC<FormDrawerProps> = ({
  title,
  open,
  onClose,
  onSubmit,
  onReset,
  children,
  width = 600,
  loading = false,
  submitText = '保存',
  resetText = '重置',
  cancelText = '取消',
  showReset = true,
}) => {
  return (
    <Drawer
      title={title}
      width={width}
      onClose={onClose}
      open={open}
      styles={{
        body: { paddingBottom: 80 },
      }}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }} disabled={loading}>
            {cancelText}
          </Button>
          {showReset && onReset && (
            <Button onClick={onReset} style={{ marginRight: 8 }} disabled={loading}>
              {resetText}
            </Button>
          )}
          <Button onClick={onSubmit} type='primary' loading={loading}>
            {submitText}
          </Button>
        </div>
      }
    >
      {children}
    </Drawer>
  );
};
