import React from 'react';
import AntdTable from './AntdTable';

/**
 * AntdTable 组件演示页面
 * 展示优化后的样式效果
 */
const AntdTableDemo: React.FC = () => {
  return (
    <div className='min-h-screen bg-gray-100'>
      <div className='py-8'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='text-center mb-8'>
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>任务管理系统</h1>
            <p className='text-lg text-gray-600'>优化后的表格组件演示</p>
          </div>

          {/* 功能特性说明 */}
          <div className='bg-white rounded-lg shadow-lg p-6 mb-8'>
            <h2 className='text-xl font-semibold text-gray-800 mb-4'>功能特性说明</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <div className='bg-blue-50 p-4 rounded-lg'>
                <h3 className='font-medium text-blue-800 mb-2'>🎨 视觉优化</h3>
                <ul className='text-sm text-blue-700 space-y-1'>
                  <li>• 渐变背景和阴影效果</li>
                  <li>• 圆角设计和现代化布局</li>
                  <li>• 响应式设计适配</li>
                </ul>
              </div>

              <div className='bg-green-50 p-4 rounded-lg'>
                <h3 className='font-medium text-green-800 mb-2'>⚡ 交互优化</h3>
                <ul className='text-sm text-green-700 space-y-1'>
                  <li>• 悬停动画效果</li>
                  <li>• 平滑过渡动画</li>
                  <li>• 优化的按钮状态</li>
                </ul>
              </div>

              <div className='bg-purple-50 p-4 rounded-lg'>
                <h3 className='font-medium text-purple-800 mb-2'>🔧 功能增强</h3>
                <ul className='text-sm text-purple-700 space-y-1'>
                  <li>• 表单标签和分组</li>
                  <li>• 状态标签美化</li>
                  <li>• 批量操作提示</li>
                </ul>
              </div>

              <div className='bg-orange-50 p-4 rounded-lg'>
                <h3 className='font-medium text-orange-800 mb-2'>📄 分页功能</h3>
                <ul className='text-sm text-orange-700 space-y-1'>
                  <li>• 支持1000条模拟数据</li>
                  <li>• 可调整每页显示数量</li>
                  <li>• 快速跳转到指定页面</li>
                  <li>• 搜索时自动重置到第一页</li>
                </ul>
              </div>
            </div>

            {/* 使用说明 */}
            <div className='mt-6 p-4 bg-gray-50 rounded-lg'>
              <h3 className='font-medium text-gray-800 mb-2'>💡 使用说明</h3>
              <div className='text-sm text-gray-600 space-y-1'>
                <p>• 表格包含1000条模拟任务数据，默认每页显示10条</p>
                <p>• 可以通过分页组件切换页面，或调整每页显示数量</p>
                <p>• 搜索和筛选功能会自动重置到第一页</p>
                <p>• 打开浏览器开发者工具可以查看分页调试日志</p>
              </div>
            </div>
          </div>

          {/* 表格组件 */}
          <AntdTable />
        </div>
      </div>
    </div>
  );
};

export default AntdTableDemo;
