import React, { useState, useCallback } from 'react';
import { ListTable } from '@visactor/react-vtable';
import { Card, Space, Button, Typography } from 'antd';

const { Title, Paragraph, Text } = Typography;

interface TableData {
  id: number;
  name: string;
  age: number;
  email: string;
}

const VTableConfigComparison: React.FC = () => {
  // 示例数据
  const [tableData] = useState<TableData[]>([
    {
      id: 1,
      name: '张三',
      age: 25,
      email: '<EMAIL>',
    },
    {
      id: 2,
      name: '李四',
      age: 30,
      email: '<EMAIL>',
    },
    {
      id: 3,
      name: '王五',
      age: 28,
      email: '<EMAIL>',
    },
  ]);

  // 方式一：直接配置属性的列定义
  const directColumns = [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      sort: true,
      headerStyle: {
        textAlign: 'center' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'center' as const,
        fontWeight: 500,
        color: '#6b7280',
      },
    },
    {
      field: 'name',
      title: '姓名',
      width: 120,
      sort: true,
      headerStyle: {
        textAlign: 'left' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'left' as const,
        fontWeight: 500,
        color: '#111827',
      },
    },
    {
      field: 'age',
      title: '年龄',
      width: 80,
      sort: true,
      headerStyle: {
        textAlign: 'center' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'center' as const,
        fontWeight: 500,
        color: '#6b7280',
      },
    },
    {
      field: 'email',
      title: '邮箱',
      width: 200,
      sort: true,
      headerStyle: {
        textAlign: 'left' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'left' as const,
        fontWeight: 400,
        color: '#4f46e5',
      },
    },
  ];

  // 方式二：通过option配置的完整配置对象
  const optionConfig = {
    records: tableData,
    columns: directColumns,
    width: 500,
    height: 300,
    widthMode: 'adaptive' as const,
    autoFillWidth: true,
    defaultRowHeight: 40,
    defaultHeaderRowHeight: 45,
    limitMinWidth: 30,
    animationAppear: false,
    theme: {
      headerStyle: {
        bgColor: '#f8fafc',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        textAlign: 'center' as const,
        borderColor: '#e5e7eb',
        borderLineWidth: 1,
      },
      bodyStyle: {
        bgColor: (args: { row: number; col: number }) => {
          return args.row % 2 === 0 ? '#ffffff' : '#f9fafb';
        },
        color: '#374151',
        fontSize: 13,
        textAlign: 'left' as const,
        borderColor: '#f3f4f6',
        borderLineWidth: 1,
      },
      frameStyle: {
        borderColor: '#e5e7eb',
        borderLineWidth: 1,
        cornerRadius: 8,
        shadowBlur: 4,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
    },
  };

  return (
    <div
      style={{
        padding: '24px',
        backgroundColor: '#f8fafc',
      }}
    >
      <Title level={2}>VTable 配置方式对比</Title>

      <Space direction='vertical' size='large' style={{ width: '100%' }}>
        {/* 说明文档 */}
        <Card
          title='📖 配置方式说明'
          style={{
            marginBottom: '24px',
          }}
        >
          <Space direction='vertical' size='middle' style={{ width: '100%' }}>
            <div>
              <Title level={4}>1. 直接配置属性方式（推荐）</Title>
              <Paragraph>
                <Text strong>特点：</Text>
                <ul>
                  <li>✅ 直接在 ListTable 组件上配置各种属性</li>
                  <li>✅ 类型安全，IDE 有完整的智能提示</li>
                  <li>✅ 符合 React 组件的使用习惯</li>
                  <li>✅ 配置清晰，易于维护</li>
                  <li>✅ 支持动态属性和回调函数</li>
                </ul>
              </Paragraph>
            </div>

            <div>
              <Title level={4}>2. Option 配置对象方式</Title>
              <Paragraph>
                <Text strong>特点：</Text>
                <ul>
                  <li>⚠️ 将所有配置集中在一个 option 对象中</li>
                  <li>⚠️ 主要用于原生 VTable（非 React 版本）</li>
                  <li>⚠️ 在 React 版本中不推荐使用</li>
                  <li>⚠️ 类型提示可能不够完整</li>
                  <li>⚠️ 不符合 React 组件的设计理念</li>
                </ul>
              </Paragraph>
            </div>
          </Space>
        </Card>

        {/* 方式一：直接配置属性 */}
        <Card
          title='✅ 方式一：直接配置属性（推荐）'
          style={{
            marginBottom: '24px',
          }}
        >
          <Paragraph>
            <Text code>
              {`<ListTable
  records={tableData}
  columns={columns}
  width={500}
  height={300}
  widthMode="adaptive"
  autoFillWidth={true}
  defaultRowHeight={40}
  theme={{...}}
  onClickCell={handleClick}
/>`}
            </Text>
          </Paragraph>

          <div
            style={{
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <ListTable
              records={tableData}
              columns={directColumns}
              width={500}
              height={300}
              widthMode='adaptive'
              autoFillWidth={true}
              defaultRowHeight={40}
              defaultHeaderRowHeight={45}
              limitMinWidth={30}
              animationAppear={false}
              theme={{
                headerStyle: {
                  bgColor: '#f8fafc',
                  color: '#374151',
                  fontSize: 14,
                  fontWeight: 600,
                  textAlign: 'center' as const,
                  borderColor: '#e5e7eb',
                  borderLineWidth: 1,
                },
                bodyStyle: {
                  bgColor: (args: { row: number; col: number }) => {
                    return args.row % 2 === 0 ? '#ffffff' : '#f9fafb';
                  },
                  color: '#374151',
                  fontSize: 13,
                  textAlign: 'left' as const,
                  borderColor: '#f3f4f6',
                  borderLineWidth: 1,
                },
                frameStyle: {
                  borderColor: '#e5e7eb',
                  borderLineWidth: 1,
                  cornerRadius: 8,
                  shadowBlur: 4,
                  shadowColor: 'rgba(0, 0, 0, 0.1)',
                },
              }}
              onClickCell={args => {
                console.log('直接配置方式 - 单元格点击:', args);
              }}
            />
          </div>
        </Card>

        {/* 方式二：Option 配置（不推荐在 React 中使用） */}
        <Card
          title='⚠️ 方式二：Option 配置对象（不推荐在 React 中使用）'
          style={{
            marginBottom: '24px',
          }}
        >
          <Paragraph>
            <Text code>
              {`// 原生 VTable 的配置方式
const option = {
  records: tableData,
  columns: columns,
  width: 500,
  height: 300,
  // ... 其他配置
};

// 在 React 中不推荐这样使用
<ListTable option={option} />`}
            </Text>
          </Paragraph>

          <Paragraph type='warning'>
            <Text strong>注意：</Text>
            @visactor/react-vtable 中的 ListTable 组件不支持 option 属性。 React 版本的 VTable 设计为直接通过 props 传递配置，这样更符合
            React 的组件设计理念。
          </Paragraph>
        </Card>

        {/* 总结 */}
        <Card title='📋 总结与建议'>
          <Space direction='vertical' size='middle' style={{ width: '100%' }}>
            <div>
              <Title level={4}>当前 VTable React 版本的配置方式</Title>
              <Paragraph>
                <ul>
                  <li>
                    <Text strong>✅ 支持：</Text>
                    直接配置属性方式（如当前代码中的使用方式）
                  </li>
                  <li>
                    <Text strong>❌ 不支持：</Text>
                    option 配置对象方式
                  </li>
                  <li>
                    <Text strong>🎯 推荐：</Text>
                    继续使用当前的直接配置属性方式
                  </li>
                </ul>
              </Paragraph>
            </div>

            <div>
              <Title level={4}>优势对比</Title>
              <Paragraph>
                <Text strong>直接配置属性方式的优势：</Text>
                <ul>
                  <li>
                    🔧 <Text strong>类型安全：</Text>
                    完整的 TypeScript 类型检查和智能提示
                  </li>
                  <li>
                    🎨 <Text strong>React 友好：</Text>
                    符合 React 组件的使用习惯
                  </li>
                  <li>
                    🔄 <Text strong>动态配置：</Text>
                    支持动态属性和状态绑定
                  </li>
                  <li>
                    📝 <Text strong>易于维护：</Text>
                    配置清晰，便于代码审查和维护
                  </li>
                  <li>
                    🎯 <Text strong>性能优化：</Text>
                    React 可以更好地优化渲染性能
                  </li>
                </ul>
              </Paragraph>
            </div>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default VTableConfigComparison;
